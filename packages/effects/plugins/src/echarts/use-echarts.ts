import type { EChartsOption } from 'echarts';

import type { Ref } from 'vue';

import type { Nullable } from '@vben/types';

import type EchartsUI from './echarts-ui.vue';

import { computed, nextTick, watch } from 'vue';

import { usePreferences } from '@vben/preferences';

import {
  tryOnUnmounted,
  useDebounceFn,
  useResizeObserver,
  useTimeoutFn,
  useWindowSize,
} from '@vueuse/core';

import echarts from './echarts';

type EchartsUIType = typeof EchartsUI | undefined;

type EchartsThemeType = 'dark' | 'light' | null;

function useEcharts(chartRef: Ref<EchartsUIType>) {
  let chartInstance: echarts.ECharts | null = null;
  let cacheOptions: EChartsOption = {};
  let originalOptions: EChartsOption = {};

  const { isDark } = usePreferences();
  const { height, width } = useWindowSize();
  const resizeHandler: () => void = useDebounceFn(resize, 200);

  const getOptions = computed((): EChartsOption => {
    const baseOptions: EChartsOption = {
      // 添加全局动画配置
      animation: true,
      animationDuration: 800,
      animationEasing: 'cubicOut',
      animationDelay: 0,
      // 确保图表从中心开始动画
      animationDurationUpdate: 600,
      animationEasingUpdate: 'cubicOut',
      // 强制设置动画阈值，确保动画始终启用
      animationThreshold: 0,
    };

    if (!isDark.value) {
      return baseOptions;
    }

    return {
      ...baseOptions,
      backgroundColor: 'transparent',
      // 确保暗色主题下动画设置不被覆盖
      animation: true,
      animationDuration: 800,
      animationEasing: 'cubicOut',
    };
  });

  const initCharts = (t?: EchartsThemeType) => {
    const el = chartRef?.value?.$el;
    if (!el) {
      return;
    }
    chartInstance = echarts.init(el, t || isDark.value ? 'dark' : null, {
      renderer: 'canvas',
    });

    return chartInstance;
  };

  const renderEcharts = (
    options: EChartsOption,
    clear = true,
  ): Promise<Nullable<echarts.ECharts>> => {
    originalOptions = options;
    const currentOptions = {
      ...options,
      ...getOptions.value,
    };
    cacheOptions = currentOptions;
    return new Promise((resolve) => {
      if (chartRef.value?.offsetHeight === 0) {
        useTimeoutFn(async () => {
          resolve(await renderEcharts(currentOptions));
        }, 30);
        return;
      }
      nextTick(() => {
        useTimeoutFn(() => {
          if (!chartInstance) {
            const instance = initCharts();
            if (!instance) return;
          }
          clear && chartInstance?.clear();

          // Add a small delay to ensure proper animation initialization
          useTimeoutFn(() => {
            chartInstance?.setOption(currentOptions, {
              notMerge: clear,
              lazyUpdate: false,
              silent: false,
            });
            resolve(chartInstance);
          }, 10);
        }, 30);
      });
    });
  };

  function resize() {
    chartInstance?.resize({
      animation: {
        duration: 300,
        easing: 'quadraticIn',
      },
    });
  }

  watch([width, height], () => {
    resizeHandler?.();
  });

  useResizeObserver(chartRef as never, resizeHandler);

  watch(isDark, () => {
    if (chartInstance) {
      chartInstance.dispose();
      initCharts();
      // Re-render with original options to ensure proper merging with new theme
      renderEcharts(originalOptions);
      resize();
    }
  });

  tryOnUnmounted(() => {
    // 销毁实例，释放资源
    chartInstance?.dispose();
  });
  return {
    renderEcharts,
    resize,
    getChartInstance: () => chartInstance,
  };
}

export { useEcharts };

export type { EchartsUIType };
