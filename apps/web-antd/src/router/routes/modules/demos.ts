import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:component',
      order: 10,
      title: $t('page.demos.title'),
    },
    name: 'Demos',
    path: '/demos',
    children: [
      {
        name: 'AntdDemo',
        path: '/antd',
        component: () => import('#/views/demos/antd/index.vue'),
        meta: {
          icon: 'simple-icons:antdesign',
          title: $t('page.demos.antd'),
        },
      },
    ],
  },
];

export default routes;